import { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import * as THREE from 'three';

const AIFace = () => {
  const headRef = useRef<THREE.Group>(null);
  const eyesRef = useRef<THREE.Group>(null);
  const particlesRef = useRef<THREE.Points>(null);
  
  useFrame((state) => {
    if (headRef.current) {
      headRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      headRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.05;
    }
    if (eyesRef.current) {
      eyesRef.current.children.forEach((eye, index) => {
        const offset = index * Math.PI;
        eye.scale.setScalar(1 + Math.sin(state.clock.elapsedTime * 3 + offset) * 0.1);
      });
    }
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.005;
    }
  });

  // Create particle system for neural network effect
  const particleCount = 100;
  const particles = new Float32Array(particleCount * 3);
  for (let i = 0; i < particleCount; i++) {
    particles[i * 3] = (Math.random() - 0.5) * 4;
    particles[i * 3 + 1] = (Math.random() - 0.5) * 4;
    particles[i * 3 + 2] = (Math.random() - 0.5) * 4;
  }

  return (
    <group ref={headRef}>
      {/* Head - main structure */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial 
          color="#1e293b" 
          roughness={0.3}
          metalness={0.7}
          transparent
          opacity={0.9}
        />
      </mesh>

      {/* Face panel */}
      <mesh position={[0, 0, 0.85]} rotation={[0, 0, 0]}>
        <planeGeometry args={[1.4, 1.8]} />
        <meshStandardMaterial 
          color="#0f172a" 
          roughness={0.1}
          metalness={0.9}
          transparent
          opacity={0.8}
        />
      </mesh>

      {/* Eyes */}
      <group ref={eyesRef}>
        <mesh position={[-0.25, 0.15, 0.9]}>
          <sphereGeometry args={[0.08, 16, 16]} />
          <meshStandardMaterial 
            color="#00ffff" 
            emissive="#00ffff" 
            emissiveIntensity={0.8}
          />
        </mesh>
        <mesh position={[0.25, 0.15, 0.9]}>
          <sphereGeometry args={[0.08, 16, 16]} />
          <meshStandardMaterial 
            color="#00ffff" 
            emissive="#00ffff" 
            emissiveIntensity={0.8}
          />
        </mesh>
      </group>

      {/* Eye glow */}
      <mesh position={[-0.25, 0.15, 0.95]}>
        <planeGeometry args={[0.3, 0.3]} />
        <meshBasicMaterial 
          color="#00ffff" 
          transparent 
          opacity={0.3}
        />
      </mesh>
      <mesh position={[0.25, 0.15, 0.95]}>
        <planeGeometry args={[0.3, 0.3]} />
        <meshBasicMaterial 
          color="#00ffff" 
          transparent 
          opacity={0.3}
        />
      </mesh>

      {/* Mouth/speaker grille */}
      <mesh position={[0, -0.3, 0.88]}>
        <boxGeometry args={[0.6, 0.1, 0.02]} />
        <meshStandardMaterial 
          color="#3b82f6" 
          emissive="#1e40af" 
          emissiveIntensity={0.2}
        />
      </mesh>
      <mesh position={[0, -0.45, 0.88]}>
        <boxGeometry args={[0.4, 0.05, 0.02]} />
        <meshStandardMaterial 
          color="#3b82f6" 
          emissive="#1e40af" 
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Neural network particles */}
      <points ref={particlesRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={particleCount}
            array={particles}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial
          color="#3b82f6"
          size={0.02}
          transparent
          opacity={0.6}
        />
      </points>

      {/* Wireframe overlay */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[1.05, 16, 16]} />
        <meshBasicMaterial 
          color="#3b82f6" 
          transparent 
          opacity={0.1}
          wireframe={true}
        />
      </mesh>
    </group>
  );
};

const AIFace3D = () => {
  return (
    <div className="w-64 h-64 md:w-80 md:h-80">
      <Canvas camera={{ position: [0, 0, 3], fov: 50 }}>
        <ambientLight intensity={0.3} />
        <pointLight position={[5, 5, 5]} intensity={1} color="#ffffff" />
        <pointLight position={[-5, -5, -5]} intensity={0.5} color="#3b82f6" />
        <pointLight position={[0, 0, 5]} intensity={0.8} color="#00ffff" />
        <AIFace />
      </Canvas>
    </div>
  );
};

export default AIFace3D;