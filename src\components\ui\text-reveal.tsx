import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TextRevealProps {
  children: string;
  className?: string;
  delay?: number;
  duration?: number;
  variant?: 'fade' | 'slide' | 'typewriter' | 'wave' | 'glitch';
}

const TextReveal: React.FC<TextRevealProps> = ({
  children,
  className = '',
  delay = 0,
  duration = 0.8,
  variant = 'fade'
}) => {
  const words = children.split(' ');
  const letters = children.split('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: variant === 'typewriter' ? 0.05 : 0.1,
        delayChildren: delay,
      },
    },
  };

  const getItemVariants = () => {
    switch (variant) {
      case 'slide':
        return {
          hidden: { y: 50, opacity: 0 },
          visible: {
            y: 0,
            opacity: 1,
            transition: { duration, ease: 'easeOut' },
          },
        };
      case 'typewriter':
        return {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: { duration: 0.1 },
          },
        };
      case 'wave':
        return {
          hidden: { y: 20, opacity: 0 },
          visible: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.5,
              ease: 'easeOut',
            },
          },
        };
      case 'glitch':
        return {
          hidden: { 
            opacity: 0,
            x: Math.random() * 20 - 10,
            y: Math.random() * 20 - 10,
          },
          visible: {
            opacity: 1,
            x: 0,
            y: 0,
            transition: { duration: 0.3, ease: 'easeOut' },
          },
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: { duration, ease: 'easeOut' },
          },
        };
    }
  };

  if (variant === 'typewriter') {
    return (
      <motion.span
        className={cn('inline-block', className)}
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {letters.map((letter, index) => (
          <motion.span
            key={index}
            variants={getItemVariants()}
            className="inline-block"
          >
            {letter === ' ' ? '\u00A0' : letter}
          </motion.span>
        ))}
        <motion.span
          className="inline-block w-0.5 h-6 bg-primary ml-1"
          animate={{ opacity: [1, 0] }}
          transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
        />
      </motion.span>
    );
  }

  return (
    <motion.span
      className={cn('inline-block', className)}
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={getItemVariants()}
          className="inline-block mr-2"
        >
          {word}
        </motion.span>
      ))}
    </motion.span>
  );
};

export { TextReveal };
