import { But<PERSON> } from "@/components/ui/button";
import { Mail, Github, Linkedin, Twitter } from "lucide-react";
import { motion } from "framer-motion";

const Footer = () => {
  const socialLinks = [
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
    { icon: Github, href: "https://github.com/xspecies-ai", label: "GitHub" },
    { icon: Linkedin, href: "https://www.linkedin.com/company/xpeciesai/", label: "LinkedIn" },
    { icon: Twitter, href: "https://twitter.com/xspecies_ai", label: "Twitter" },
  ];

  return (
    <motion.footer
      id="footer"
      className="relative border-t border-border bg-card/20 mt-auto"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      viewport={{ once: true, margin: "-100px" }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16 xl:py-20">
        {/* Get in Touch Section */}
        <motion.div
          className="text-center space-y-3 sm:space-y-4 lg:space-y-6 mb-6 sm:mb-8 lg:mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            Get in <span className="gradient-text">Touch</span>
          </motion.h2>
          <motion.p
            className="text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            Ready to explore the future of robotics? Let's connect and discuss how we can
            transform your business with our AI-powered solutions.
          </motion.p>

          {/* Social Links */}
          <motion.div
            className="flex items-center justify-center space-x-4 sm:space-x-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            {socialLinks.map((social, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.6 + index * 0.1, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="ghost"
                  size="lg"
                  className="w-10 h-10 sm:w-12 sm:h-12 rounded-full hover:bg-primary/10 hover:text-primary transition-smooth hover-scale"
                  asChild
                >
                  <a
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={social.label}
                    className="flex items-center justify-center"
                  >
                    <social.icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                </Button>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Footer Bottom */}
        <motion.div
          className="border-t border-border pt-4 sm:pt-6"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col lg:flex-row items-center justify-center lg:justify-between space-y-3 lg:space-y-0 gap-4">
            {/* Logo */}
            <motion.div
              className="flex items-center"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <span className="text-lg sm:text-xl font-bold gradient-text">xSpecies AI</span>
            </motion.div>

            {/* Copyright */}
            <motion.div
              className="text-center text-sm sm:text-base text-muted-foreground"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              © 2025 xSpecies AI. All rights reserved. Made in India 🇮🇳
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom spacing for mobile navigation */}
        <div className="h-12 sm:h-14 lg:hidden"></div>
      </div>
    </motion.footer>
  );
};

export default Footer;