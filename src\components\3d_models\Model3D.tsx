import { useRef, Suspense, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

// Component to load and display the GLB model
const Model = () => {
  const modelRef = useRef<THREE.Group>(null);

  // Load the GLB model
  const { scene } = useGLTF('/models/make_a_3d_model_of_an_0802154730_texture.glb');

  // Add smooth floating animation
  useFrame((state) => {
    if (modelRef.current) {
      // Gentle floating motion
      modelRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.1;
      // Slow rotation
      modelRef.current.rotation.y = state.clock.elapsedTime * 0.2;
      // Subtle tilt
      modelRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
    }
  });

  return (
    <group ref={modelRef}>
      <primitive object={scene} scale={[2, 2, 2]} />
    </group>
  );
};

// Loading fallback component with futuristic styling
const LoadingFallback = () => (
  <group>
    <mesh>
      <sphereGeometry args={[1, 16, 16]} />
      <meshStandardMaterial
        color="#3b82f6"
        wireframe
        transparent
        opacity={0.6}
      />
    </mesh>
    <mesh>
      <sphereGeometry args={[1.1, 8, 8]} />
      <meshBasicMaterial
        color="#00ffff"
        wireframe
        transparent
        opacity={0.3}
      />
    </mesh>
  </group>
);

// Main 3D Model component
const Model3D = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className="w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem] relative">
      {/* Glow effect background */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-glow/20 rounded-full blur-3xl"></div>

      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        <Suspense fallback={<LoadingFallback />}>
          {/* Enhanced lighting setup - consistent across all devices */}
          <ambientLight intensity={0.4} />
          <pointLight position={[5, 5, 5]} intensity={1.2} color="#ffffff" />
          <pointLight position={[-5, -5, -5]} intensity={0.8} color="#3b82f6" />
          <pointLight position={[0, 0, 10]} intensity={0.6} color="#00ffff" />
          <directionalLight position={[0, 10, 0]} intensity={0.5} />

          {/* Environment for better reflections */}
          <Environment preset="city" />

          {/* The 3D Model */}
          <Model />

          {/* Interactive controls */}
          <OrbitControls
            enableZoom={!isMobile}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={isMobile ? 0.3 : 0.5}
            maxPolarAngle={Math.PI / 1.5}
            minPolarAngle={Math.PI / 3}
            maxDistance={8}
            minDistance={3}
            enableDamping={true}
            dampingFactor={0.05}
          />
        </Suspense>
      </Canvas>
    </div>
  );
};

// Preload the model for better performance
useGLTF.preload('/models/make_a_3d_model_of_an_0802154730_texture.glb');

export default Model3D;
