import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardProps } from './card';
import { cn } from '@/lib/utils';

interface EnhancedCardProps extends CardProps {
  variant?: 'default' | 'glass' | 'neon' | 'gradient' | 'floating';
  glow?: boolean;
  tilt?: boolean;
  magnetic?: boolean;
  children?: React.ReactNode;
  className?: string;
}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({
    className,
    variant = 'default',
    glow = false,
    tilt = false,
    magnetic = false,
    children,
    ...props
  }, ref) => {
    const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 });
    const [isHovered, setIsHovered] = React.useState(false);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!tilt && !magnetic) return;

      const rect = e.currentTarget.getBoundingClientRect();
      const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
      const y = (e.clientY - rect.top - rect.height / 2) / rect.height;

      setMousePosition({ x, y });
    };

    const handleMouseLeave = () => {
      setMousePosition({ x: 0, y: 0 });
      setIsHovered(false);
    };

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const getVariantClasses = () => {
      switch (variant) {
        case 'glass':
          return 'glass-card backdrop-blur-xl border-white/10';
        case 'neon':
          return 'bg-card/50 border-primary/50 hover:border-primary hover:shadow-neon';
        case 'gradient':
          return 'bg-gradient-to-br from-card via-card/80 to-card/60 border-gradient';
        case 'floating':
          return 'bg-card/80 backdrop-blur-sm hover-float';
        default:
          return 'bg-card/30';
      }
    };

    const getTiltTransform = () => {
      if (!tilt) return {};

      return {
        rotateX: mousePosition.y * 10,
        rotateY: mousePosition.x * 10,
        scale: isHovered ? 1.05 : 1,
      };
    };

    const getMagneticTransform = () => {
      if (!magnetic) return {};

      return {
        x: mousePosition.x * 20,
        y: mousePosition.y * 20,
      };
    };

    return (
      <motion.div
        style={{ perspective: 1000 }}
        whileHover={{ scale: magnetic ? 1.02 : 1 }}
      >
        <motion.div
          animate={{
            ...getTiltTransform(),
            ...getMagneticTransform(),
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          onMouseEnter={handleMouseEnter}
          className="relative"
        >
          <Card
            ref={ref}
            className={cn(
              'relative overflow-hidden transition-all duration-300 border',
              getVariantClasses(),
              glow && 'hover:glow-primary',
              tilt && 'transform-gpu',
              className
            )}
            {...props}
          >
            {/* Animated border gradient */}
            {variant === 'neon' && isHovered && (
              <motion.div
                className="absolute inset-0 rounded-lg"
                style={{
                  background: 'linear-gradient(45deg, transparent, hsl(var(--primary)), transparent)',
                  padding: '1px',
                }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="w-full h-full bg-card rounded-lg" />
              </motion.div>
            )}

            {/* Spotlight effect */}
            {(tilt || magnetic) && isHovered && (
              <motion.div
                className="absolute inset-0 opacity-20 pointer-events-none"
                style={{
                  background: `radial-gradient(circle at ${(mousePosition.x + 1) * 50}% ${(mousePosition.y + 1) * 50}%, hsl(var(--primary)) 0%, transparent 50%)`,
                }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.2 }}
                exit={{ opacity: 0 }}
              />
            )}

            {/* Content */}
            <div className="relative z-10">
              {children}
            </div>
          </Card>
        </motion.div>
      </motion.div>
    );
  }
);

EnhancedCard.displayName = 'EnhancedCard';

export { EnhancedCard };
export type { EnhancedCardProps };
