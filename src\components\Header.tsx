import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import { Mail, User, Package, Eye, BookOpen } from "lucide-react";
import { motion, useScroll, useTransform } from "framer-motion";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { scrollY } = useScroll();

  // Transform values for scroll-based animations
  const headerOpacity = useTransform(scrollY, [0, 100], [0.95, 0.98]);
  const headerBlur = useTransform(scrollY, [0, 100], [8, 16]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: "About", href: "/#about", isRoute: false },
    { name: "Products", href: "/products", isRoute: true },
    { name: "Vision", href: "/#vision", isRoute: false },
    { name: "Blog", href: "/#blog", isRoute: false },
    { name: "Contact", href: "/#contact", isRoute: false },
  ];

  const getNavIcon = (itemName: string) => {
    switch (itemName) {
      case "About":
        return User;
      case "Products":
        return Package;
      case "Vision":
        return Eye;
      case "Blog":
        return BookOpen;
      case "Contact":
        return Mail;
      default:
        return User;
    }
  };

  const scrollToFooter = () => {
    const footer = document.querySelector('footer');
    if (footer) {
      footer.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  return (
    <>
      {/* Mobile Top Navbar - Only visible on mobile */}
      <motion.header
        className="lg:hidden fixed top-0 left-0 right-0 z-50 border-b border-border/50 shadow-sm"
        style={{
          backgroundColor: `rgba(var(--background), ${headerOpacity.get()})`,
          backdropFilter: `blur(${headerBlur.get()}px)`
        }}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-5">
          <div className="flex items-center justify-center">
            <motion.span
              className="text-2xl sm:text-3xl font-bold gradient-text"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            >
              xSpecies AI
            </motion.span>
          </div>
        </div>
      </motion.header>

      {/* Desktop Header */}
      <motion.header
        className="hidden lg:block fixed top-0 left-0 right-0 z-50 border-b border-border/50 shadow-sm"
        style={{
          backgroundColor: `rgba(var(--background), ${headerOpacity.get()})`,
          backdropFilter: `blur(${headerBlur.get()}px)`
        }}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          <nav className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="hover:opacity-80 transition-smooth">
              <motion.span
                className="text-lg sm:text-xl lg:text-2xl font-bold gradient-text"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              >
                xSpecies AI
              </motion.span>
            </Link>

            {/* Desktop Navigation */}
            <motion.div
              className="hidden lg:flex items-center space-x-6 xl:space-x-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            >
              {navItems.filter(item => item.name !== "Contact").map((item, index) =>
                item.isRoute ? (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1, ease: "easeOut" }}
                  >
                    <Link
                      to={item.href}
                      className={`text-sm xl:text-base font-medium transition-smooth relative group ${
                        location.pathname === item.href
                          ? 'text-primary'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      {item.name}
                      <span className={`absolute -bottom-1 left-0 h-0.5 bg-gradient-primary transition-all duration-300 rounded-full ${
                        location.pathname === item.href ? 'w-full' : 'w-0 group-hover:w-full'
                      }`}></span>
                    </Link>
                  </motion.div>
                ) : (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1, ease: "easeOut" }}
                  >
                    <a
                      href={item.href}
                      className="text-sm xl:text-base text-muted-foreground hover:text-foreground transition-smooth relative group font-medium"
                    >
                      {item.name}
                      <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full rounded-full"></span>
                    </a>
                  </motion.div>
                )
              )}
            </motion.div>

            {/* Get in Touch Button - Desktop Only */}
            <motion.div
              className="hidden lg:block"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
            >
              <Button
                variant="outline"
                size="sm"
                className="border-primary/20 hover:border-primary hover:bg-primary/10 transition-smooth font-medium text-sm xl:text-base"
                onClick={scrollToFooter}
              >
                Get in Touch
              </Button>
            </motion.div>

          </nav>
        </div>
      </motion.header>

      {/* Mobile Bottom Navigation */}
      <motion.div
        className="lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-lg border-t border-border/50 shadow-lg"
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
      >
        <div className="flex items-center justify-around py-2 sm:py-3 px-2 safe-area-inset-bottom">
          {navItems.map((item, index) => {
            const IconComponent = getNavIcon(item.name);

            return item.name === "Contact" ? (
              <motion.button
                key={item.name}
                onClick={scrollToFooter}
                className="flex flex-col items-center space-y-1 p-2 sm:p-3 rounded-lg hover:bg-primary/5 transition-smooth min-w-0 flex-1 max-w-[80px]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1, ease: "easeOut" }}
                whileTap={{ scale: 0.95 }}
              >
                <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground hover:text-primary transition-colors" />
                <span className="text-xs sm:text-sm font-medium text-muted-foreground hover:text-primary transition-colors text-center leading-tight">
                  {item.name}
                </span>
              </motion.button>
            ) : item.isRoute ? (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1, ease: "easeOut" }}
              >
                <Link
                  to={item.href}
                  className="flex flex-col items-center space-y-1 p-2 sm:p-3 rounded-lg hover:bg-primary/5 transition-smooth min-w-0 flex-1 max-w-[80px]"
                >
                  <IconComponent className={`w-4 h-4 sm:w-5 sm:h-5 transition-all ${
                    location.pathname === item.href ? 'text-primary scale-110' : 'text-muted-foreground'
                  }`} />
                  <span className={`text-xs sm:text-sm font-medium transition-colors text-center leading-tight ${
                    location.pathname === item.href ? 'text-primary' : 'text-muted-foreground'
                  }`}>
                    {item.name}
                  </span>
                </Link>
              </motion.div>
            ) : (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1, ease: "easeOut" }}
              >
                <a
                  href={item.href}
                  className="flex flex-col items-center space-y-1 p-2 sm:p-3 rounded-lg hover:bg-primary/5 transition-smooth min-w-0 flex-1 max-w-[80px]"
                >
                  <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground hover:text-primary transition-all hover:scale-110" />
                  <span className="text-xs sm:text-sm font-medium text-muted-foreground hover:text-primary transition-colors text-center leading-tight">
                    {item.name}
                  </span>
                </a>
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    </>
  );
};

export default Header;