import React from 'react';
import { motion } from 'framer-motion';
import { Button, ButtonProps } from './button';
import { cn } from '@/lib/utils';

interface EnhancedButtonProps extends ButtonProps {
  variant?: 'default' | 'neon' | 'glass' | 'magnetic' | 'pulse';
  glow?: boolean;
  ripple?: boolean;
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ className, variant = 'default', glow = false, ripple = false, children, ...props }, ref) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([]);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (ripple) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const newRipple = { id: Date.now(), x, y };
        
        setRipples(prev => [...prev, newRipple]);
        
        setTimeout(() => {
          setRipples(prev => prev.filter(r => r.id !== newRipple.id));
        }, 600);
      }
      
      if (props.onClick) {
        props.onClick(e);
      }
    };

    const getVariantClasses = () => {
      switch (variant) {
        case 'neon':
          return 'bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground hover:shadow-neon';
        case 'glass':
          return 'glass-card text-foreground hover:bg-white/10';
        case 'magnetic':
          return 'magnetic bg-gradient-primary hover:scale-105 hover:-translate-y-1';
        case 'pulse':
          return 'bg-gradient-primary pulse-glow';
        default:
          return '';
      }
    };

    return (
      <motion.div
        whileHover={{ scale: variant === 'magnetic' ? 1.05 : 1.02 }}
        whileTap={{ scale: 0.98 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Button
          ref={ref}
          className={cn(
            'relative overflow-hidden transition-all duration-300',
            getVariantClasses(),
            glow && 'hover:glow-primary',
            className
          )}
          onClick={handleClick}
          {...props}
        >
          {/* Ripple effect */}
          {ripple && ripples.map(ripple => (
            <motion.span
              key={ripple.id}
              className="absolute bg-white/30 rounded-full pointer-events-none"
              style={{
                left: ripple.x - 10,
                top: ripple.y - 10,
                width: 20,
                height: 20,
              }}
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 4, opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />
          ))}
          
          {/* Hover glow effect */}
          {isHovered && variant === 'neon' && (
            <motion.div
              className="absolute inset-0 bg-primary/20 rounded-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />
          )}
          
          {/* Content */}
          <span className="relative z-10 flex items-center justify-center gap-2">
            {children}
          </span>
        </Button>
      </motion.div>
    );
  }
);

EnhancedButton.displayName = 'EnhancedButton';

export { EnhancedButton };
export type { EnhancedButtonProps };
