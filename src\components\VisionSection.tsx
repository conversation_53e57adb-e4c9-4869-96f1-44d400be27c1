import { motion } from "framer-motion";

const VisionSection = () => {
  return (
    <section id="vision" className="py-12 sm:py-16 lg:py-20 xl:py-24 relative">
      <div className="absolute inset-0 bg-gradient-hero"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-5xl mx-auto text-center space-y-6 sm:space-y-8 lg:space-y-12 xl:space-y-16">
          {/* Section Header */}
          <motion.div
            className="space-y-3 sm:space-y-4 lg:space-y-6"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.h2
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              Our <span className="gradient-text">Vision</span>
            </motion.h2>
            <motion.p
              className="text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto px-2 sm:px-0"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              We envision a future where human capabilities are augmented beyond current mental
              and physical limits with the aid of AI, ushering in a new era of human progress and
              prosperity.
            </motion.p>
          </motion.div>

          {/* Mission Statement */}
          <motion.div
            className="bg-card/30 border border-border rounded-xl sm:rounded-2xl lg:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 backdrop-blur-sm shadow-card hover-lift"
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ scale: 1.02, y: -5 }}
          >
            <div className="space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8">
              <motion.h3
                className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
                viewport={{ once: true }}
              >
                Our Mission
              </motion.h3>
              <motion.p
                className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-muted-foreground leading-relaxed text-center max-w-4xl mx-auto px-2 sm:px-0"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
                viewport={{ once: true }}
              >
                Our mission is to develop a foundational, end-to-end technology stack for general
                purpose robots that will be manufactured and deployed at scale in commercial
                applications by 2026 to significantly improve operational productivity and efficiency.
                This will culminate into the development and launch of our fully autonomous
                Humanoid for general purpose use in both business as well as consumer applications
                by the year 2027.
              </motion.p>
            </div>
          </motion.div>

          {/* Timeline */}
          <div></div>
        </div>
      </div>
    </section>
  );
};

export default VisionSection;