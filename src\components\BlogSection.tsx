import { Card, CardContent } from "@/components/ui/card";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import { TextReveal } from "@/components/ui/text-reveal";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Calendar, Clock, X } from "lucide-react";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

const BlogSection = () => {
  const [selectedPost, setSelectedPost] = useState<number | null>(null);

  // Prevent background scrolling when modal is open
  const openModal = (index: number) => {
    setSelectedPost(index);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setSelectedPost(null);
    document.body.style.overflow = 'unset';
  };

  const blogPosts = [
    {
      title: "Why the Future of Robotics Is Not Just Industrial—It's Personal",
      excerpt: "For decades, robots have been confined to factories. But the next frontier lies in our homes, hospitals, offices, and communities...",
      date: "December 15, 2024",
      readTime: "5 min read",
      category: "Future Tech",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop",
      content: `For decades, robots have been confined to the well-lit, perfectly structured corridors of factories. They've built our cars, sorted our goods, and optimized our production lines. But the next frontier of robotics lies not in the warehouse, but in the world we live in: our homes, hospitals, offices, and communities.

At Species AI, we believe robots should do more than just automate. They should assist, interact, and adapt to us. Whether it's helping an elderly parent carry groceries, assisting a nurse with repetitive hospital tasks, or responding to a child's question with empathy, the future of robotics is personal.

We're building humanoid systems that can understand, reason, and act in unstructured environments. By combining powerful AI with general-purpose hardware, our robots are designed to integrate seamlessly into human spaces. This isn't science fiction. It's a shift in how we think about machines—not as tools, but as collaborators.`
    },
    {
      title: "Building Intelligence into Movement: The Species AI Approach",
      excerpt: "One of the hardest problems in robotics is manipulation. At Species AI, we're solving this with AIWare—our unified AI platform...",
      date: "December 12, 2024",
      readTime: "7 min read",
      category: "Technology",
      image: "/lovable-uploads/a155def6-a5e4-4259-a31c-2fc9b0962620.png",
      content: `One of the hardest problems in robotics is manipulation. Picking up a transparent bottle, gripping a pouch of lentils, or placing a fragile item on a shelf might seem trivial to humans, but for robots, these are monumental tasks.

At Species AI, we're solving this with AIWare—our unified AI platform for intelligent robotic control. Instead of relying on rule-based scripts, our robots learn. Using a combination of simulation training (Sim2Real), human teleoperation, and reinforcement learning, we teach our systems to adapt in real time.

What makes AIWare special is its ability to reason. Vision-Language Models (VLMs) help our systems understand not just what an object is, but how to interact with it. A robot can decide to grab a bottle from the side, not the top. It can know not to crush a fruit. These decisions aren't programmed—they're learned.

By embedding intelligence into movement, we're turning robotic arms into true universal manipulators. And we're bringing that same intelligence into our humanoids.`
    },
    {
      title: "Why India Needs Its Own Humanoid Robot",
      excerpt: "India is on the cusp of a demographic transformation. By 2050, over 140 million Indians will be over the age of 60...",
      date: "December 10, 2024",
      readTime: "6 min read",
      category: "Industry",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop",
      content: `India is on the cusp of a demographic transformation. By 2050, over 140 million Indians will be over the age of 60. At the same time, there's a growing shortage of skilled labor across industries, from healthcare and logistics to retail and security.

In most countries, this challenge is being addressed through automation and robotics. But off-the-shelf robots designed in the West aren't made for India's diversity. Our groceries, our environments, our people—they're different.

That's why we're building MANAV—a humanoid robot designed in and for India. MANAV isn't just a marvel of engineering. It's a statement of intent. It's built to assist caregivers, aid in healthcare, and support workers in settings where traditional robots simply can't go.

With AIWare as its brain, and thoughtful design as its body, MANAV represents the future of Indian robotics. It's time we stopped importing the future and started building it ourselves.`
    }
  ];

  return (
    <section id="blog" className="py-16 sm:py-20 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center space-y-4 lg:space-y-6 mb-12 sm:mb-16 lg:mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
            <TextReveal variant="slide" delay={0.2}>Latest</TextReveal>{" "}
            <span className="gradient-text">Updates</span>
          </h2>
          <motion.p
            className="text-base sm:text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            Stay updated with our latest insights, developments, and breakthrough innovations
            in AI and robotics technology.
          </motion.p>
        </motion.div>

        {/* Blog Posts Grid */}
        <motion.div
          className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {blogPosts.map((post, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1, ease: "easeOut" }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
            >
              <Card className="group bg-card/30 border-border hover-lift overflow-hidden transition-all duration-300 h-full flex flex-col">
              <div className="aspect-video bg-gradient-secondary relative overflow-hidden">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
                  <span className="px-2 sm:px-3 py-1 text-xs sm:text-sm font-medium bg-primary/90 text-primary-foreground rounded-full">
                    {post.category}
                  </span>
                </div>
              </div>

              <CardContent className="p-4 sm:p-6 space-y-3 sm:space-y-4 flex-1 flex flex-col">
                <div className="flex items-center space-x-3 sm:space-x-4 text-xs sm:text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="truncate">{post.date}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                <h3 className="text-lg sm:text-xl lg:text-2xl font-bold group-hover:text-primary transition-colors leading-tight">
                  {post.title}
                </h3>

                <p className="text-sm sm:text-base text-muted-foreground line-clamp-3 leading-relaxed flex-1">
                  {post.excerpt}
                </p>

                <Button
                  variant="ghost"
                  className="group/btn p-0 h-auto text-primary hover:text-primary-glow transition-colors self-start text-sm sm:text-base"
                  onClick={() => openModal(index)}
                >
                  Read More
                  <ArrowRight className="ml-1 w-3 h-3 sm:w-4 sm:h-4 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Blog Post Modal */}
      <AnimatePresence>
        {selectedPost !== null && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            onClick={closeModal}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="bg-background border border-border rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
              onClick={(e) => e.stopPropagation()}
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 50 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
            {/* Modal Header */}
            <div className="relative">
              <img
                src={blogPosts[selectedPost].image}
                alt={blogPosts[selectedPost].title}
                className="w-full h-48 sm:h-64 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <button
                onClick={closeModal}
                className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-white" />
              </button>
              <div className="absolute bottom-4 left-4">
                <span className="px-3 py-1 text-sm font-medium bg-primary/90 text-primary-foreground rounded-full">
                  {blogPosts[selectedPost].category}
                </span>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 sm:p-8 overflow-y-auto max-h-[calc(90vh-16rem)]">
              <div className="space-y-4 sm:space-y-6">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight">
                  {blogPosts[selectedPost].title}
                </h1>

                <div className="prose prose-lg max-w-none text-muted-foreground leading-relaxed">
                  {blogPosts[selectedPost].content.split('\n\n').map((paragraph, idx) => (
                    <p key={idx} className="mb-4 text-base sm:text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default BlogSection;